<?php

/**
 * Provide a admin area view for managing bids
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}

// Get all auction lots for the filter
$lots = get_posts(array(
    'post_type' => 'post',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC',
    'meta_query' => array(
        array(
            'key' => '_vicino_auction_enabled',
            'value' => '1',
            'compare' => '='
        )
    )
));

// Handle lot filter
$selected_lot = isset($_GET['lot_filter']) ? intval($_GET['lot_filter']) : 0;

// Get bids with optional lot filter
global $wpdb;
$table_name = $wpdb->prefix . 'vicino_auction_bids';
$where_clause = $selected_lot ? "WHERE lot_id = $selected_lot" : '';
$bids = $wpdb->get_results("SELECT * FROM $table_name $where_clause ORDER BY bid_date DESC");
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

    <div class="tablenav top">
        <div class="alignleft actions">
            <form method="get" action="">
                <input type="hidden" name="page" value="<?php echo esc_attr($_REQUEST['page']); ?>">
                <select name="lot_filter">
                    <option value=""><?php _e('Todos los Lotes', 'vicino-auction'); ?></option>
                    <?php foreach ($lots as $lot) : ?>
                        <option value="<?php echo esc_attr($lot->ID); ?>" <?php selected($selected_lot, $lot->ID); ?>>
                            <?php echo esc_html($lot->post_title); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <?php submit_button(__('Filtrar', 'vicino-auction'), 'action', '', false); ?>
            </form>
        </div>
        <br class="clear">
    </div>
    
    <?php if (empty($bids)) : ?>
        <p><?php _e('Aún no se han realizado Preofertas.', 'vicino-auction'); ?></p>
    <?php else : ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Lote', 'vicino-auction'); ?></th>
                    <th><?php _e('Preofertante', 'vicino-auction'); ?></th>
                    <th><?php _e('Monto', 'vicino-auction'); ?></th>
                    <th><?php _e('Fecha', 'vicino-auction'); ?></th>
                    <th><?php _e('Estado', 'vicino-auction'); ?></th>
                    <th><?php _e('Acciones', 'vicino-auction'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($bids as $bid) : 
                    $lot = get_post($bid->lot_id);
                    $user = get_user_by('id', $bid->user_id);
                    $lot_obj = new Vicino_Auction_Lot();
                ?>
                <tr>
                    <td>
                        <?php if ($lot) : ?>
                            <a href="<?php echo get_edit_post_link($bid->lot_id); ?>"><?php echo $lot->post_title; ?></a>
                        <?php else : ?>
                            <?php _e('Lote Desconocido', 'vicino-auction'); ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($user) : ?>
                            <a href="<?php echo get_edit_user_link($bid->user_id); ?>"><?php echo $user->display_name; ?></a>
                        <?php else : ?>
                            <?php _e('Usuario Desconocido', 'vicino-auction'); ?>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $lot_obj->format_price($bid->bid_amount); ?></td>
                    <td><?php
                        // Convert UTC stored bid date to Argentina timezone (-3 hours)
                        $bid_timestamp = strtotime($bid->bid_date . ' UTC');
                        $argentina_timestamp = $bid_timestamp - (3 * 3600); // Subtract 3 hours for Argentina timezone
                        echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $argentina_timestamp);
                    ?></td>
                    <td>
                        <?php if ($bid->bid_status == 'active') : ?>
                            <span class="status-active"><?php _e('Activa', 'vicino-auction'); ?></span>
                        <?php else : ?>
                            <span class="status-inactive"><?php _e('Inactiva', 'vicino-auction'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a href="#" class="delete-bid" data-bid-id="<?php echo $bid->id; ?>" data-nonce="<?php echo wp_create_nonce('delete_bid_nonce'); ?>">
                            <?php _e('Eliminar', 'vicino-auction'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>